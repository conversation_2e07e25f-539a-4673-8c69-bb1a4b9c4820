// 小朋友積分獎勵系統 JavaScript

class BabyAwardsApp {
    constructor() {
        this.data = this.loadData();
        this.init();
    }

    // 初始化應用
    init() {
        this.setupEventListeners();
        this.updateDisplay();
        this.showTab('home');
    }

    // 設置事件監聽器
    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('data-tab');
                this.showTab(tabName);
            });
        });

        // Add points form
        document.getElementById('add-points-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addPoints();
        });

        // Quick reason buttons
        document.querySelectorAll('.quick-reason').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const reason = e.target.getAttribute('data-reason');
                const points = e.target.getAttribute('data-points');
                document.getElementById('reason').value = reason;
                document.getElementById('points').value = points;
            });
        });

        // Add goal form
        document.getElementById('add-goal-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addGoal();
        });
    }

    // 顯示指定的標籤頁
    showTab(tabName) {
        // Hide all tabs
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });

        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('border-blue-500', 'text-blue-600');
            btn.classList.add('border-transparent', 'text-gray-600');
        });

        // Show selected tab
        document.getElementById(tabName).classList.add('active');

        // Add active class to selected tab button
        const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
        activeBtn.classList.remove('border-transparent', 'text-gray-600');
        activeBtn.classList.add('border-blue-500', 'text-blue-600');

        // Update display when switching tabs
        this.updateDisplay();
    }

    // 添加積分
    addPoints() {
        const reason = document.getElementById('reason').value.trim();
        const points = parseInt(document.getElementById('points').value);

        if (!reason || !points || points <= 0) {
            this.showMessage('請填寫完整的積分資訊', 'error');
            return;
        }

        const pointRecord = {
            id: Date.now(),
            reason: reason,
            points: points,
            date: new Date().toISOString().split('T')[0],
            timestamp: new Date().toLocaleString('zh-TW')
        };

        this.data.pointsHistory.unshift(pointRecord);
        this.data.currentPoints += points;

        this.saveData();
        this.updateDisplay();
        this.showMessage(`成功添加 ${points} 分！`, 'success');

        // Reset form
        document.getElementById('add-points-form').reset();

        // Check if any goals are completed
        this.checkGoalCompletion();
    }

    // 添加目標
    addGoal() {
        const title = document.getElementById('goal-title').value.trim();
        const targetPoints = parseInt(document.getElementById('target-points').value);

        if (!title || !targetPoints || targetPoints <= 0) {
            this.showMessage('請填寫完整的目標資訊', 'error');
            return;
        }

        const goal = {
            id: Date.now(),
            title: title,
            targetPoints: targetPoints,
            isCompleted: false,
            createdAt: new Date().toISOString().split('T')[0]
        };

        this.data.goals.push(goal);
        this.saveData();
        this.updateDisplay();
        this.showMessage('目標設定成功！', 'success');

        // Reset form
        document.getElementById('add-goal-form').reset();
    }

    // 完成目標
    completeGoal(goalId) {
        const goal = this.data.goals.find(g => g.id === goalId);
        if (!goal) return;

        if (this.data.currentPoints < goal.targetPoints) {
            this.showMessage('積分不足，無法完成此目標', 'error');
            return;
        }

        // Move to achievements
        const achievement = {
            goalTitle: goal.title,
            targetPoints: goal.targetPoints,
            completedAt: new Date().toLocaleString('zh-TW')
        };

        this.data.achievements.unshift(achievement);
        this.data.currentPoints -= goal.targetPoints;

        // Remove from goals
        this.data.goals = this.data.goals.filter(g => g.id !== goalId);

        this.saveData();
        this.updateDisplay();
        this.showMessage(`恭喜完成目標：${goal.title}！`, 'success');
    }

    // 檢查目標完成
    checkGoalCompletion() {
        const completableGoals = this.data.goals.filter(goal => 
            !goal.isCompleted && this.data.currentPoints >= goal.targetPoints
        );

        if (completableGoals.length > 0) {
            this.showMessage(`有 ${completableGoals.length} 個目標可以完成了！`, 'info');
        }
    }

    // 更新顯示
    updateDisplay() {
        this.updateCurrentPoints();
        this.updateCurrentGoals();
        this.updateRecentActivities();
        this.updateGoalsList();
        this.updatePointsHistory();
        this.updateAchievements();
    }

    // 更新當前積分顯示
    updateCurrentPoints() {
        document.getElementById('current-points').textContent = this.data.currentPoints;
    }

    // 更新當前目標顯示
    updateCurrentGoals() {
        const container = document.getElementById('current-goals-list');
        
        if (this.data.goals.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">還沒有設定目標，快去設定一個吧！</p>';
            return;
        }

        container.innerHTML = this.data.goals.map(goal => {
            const progress = Math.min((this.data.currentPoints / goal.targetPoints) * 100, 100);
            const canComplete = this.data.currentPoints >= goal.targetPoints;

            return `
                <div class="border border-gray-200 rounded-lg p-4 mb-3">
                    <div class="flex justify-between items-start mb-2">
                        <h4 class="font-semibold text-gray-800">${goal.title}</h4>
                        <span class="text-sm text-gray-600">${this.data.currentPoints}/${goal.targetPoints}分</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: ${progress}%"></div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">${progress.toFixed(1)}% 完成</span>
                        ${canComplete ? 
                            `<button onclick="app.completeGoal(${goal.id})" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-check mr-1"></i>完成目標
                            </button>` : 
                            `<span class="text-sm text-gray-500">還需要 ${goal.targetPoints - this.data.currentPoints} 分</span>`
                        }
                    </div>
                </div>
            `;
        }).join('');
    }

    // 更新最近活動
    updateRecentActivities() {
        const container = document.getElementById('recent-activities');
        const recentActivities = this.data.pointsHistory.slice(0, 5);

        if (recentActivities.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">還沒有積分記錄</p>';
            return;
        }

        container.innerHTML = recentActivities.map(activity => `
            <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <div>
                    <p class="font-medium text-gray-800">${activity.reason}</p>
                    <p class="text-sm text-gray-600">${activity.timestamp}</p>
                </div>
                <span class="text-green-600 font-bold">+${activity.points}分</span>
            </div>
        `).join('');
    }

    // 刪除目標
    deleteGoal(goalId) {
        if (confirm('確定要刪除這個目標嗎？')) {
            this.data.goals = this.data.goals.filter(g => g.id !== goalId);
            this.saveData();
            this.updateDisplay();
            this.showMessage('目標已刪除', 'success');
        }
    }

    // 顯示消息
    showMessage(message, type = 'info') {
        const container = document.getElementById('message-container');
        const messageId = Date.now();

        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            info: 'bg-blue-500',
            warning: 'bg-yellow-500'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle'
        };

        const messageElement = document.createElement('div');
        messageElement.id = `message-${messageId}`;
        messageElement.className = `${colors[type]} text-white px-4 py-3 rounded-lg shadow-lg mb-2 flex items-center transform translate-x-full transition-transform duration-300`;
        messageElement.innerHTML = `
            <i class="${icons[type]} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(messageElement);

        // Animate in
        setTimeout(() => {
            messageElement.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (messageElement.parentElement) {
                messageElement.classList.add('translate-x-full');
                setTimeout(() => {
                    if (messageElement.parentElement) {
                        messageElement.remove();
                    }
                }, 300);
            }
        }, 3000);
    }

    // 載入數據
    loadData() {
        const defaultData = {
            currentPoints: 0,
            goals: [],
            pointsHistory: [],
            achievements: []
        };

        try {
            const savedData = localStorage.getItem('babyAwardsData');
            return savedData ? { ...defaultData, ...JSON.parse(savedData) } : defaultData;
        } catch (error) {
            console.error('載入數據時發生錯誤:', error);
            return defaultData;
        }
    }

    // 保存數據
    saveData() {
        try {
            localStorage.setItem('babyAwardsData', JSON.stringify(this.data));
        } catch (error) {
            console.error('保存數據時發生錯誤:', error);
            this.showMessage('保存數據失敗', 'error');
        }
    }
}

// 初始化應用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new BabyAwardsApp();
});
