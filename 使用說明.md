# 小朋友積分獎勵系統 - 使用說明

## 🚀 快速開始

1. **打開應用**: 在瀏覽器中打開 `index.html` 文件
2. **設定第一個目標**: 點擊「目標管理」標籤，為小朋友設定一個目標
3. **記錄積分**: 點擊「記錄積分」標籤，當小朋友表現良好時記錄積分
4. **追蹤進度**: 在「首頁」查看目標進度
5. **完成目標**: 當積分足夠時，點擊「完成目標」按鈕

## 📱 功能介紹

### 首頁
- **當前積分**: 顯示小朋友目前累積的總積分
- **進行中的目標**: 顯示所有設定的目標和完成進度
- **最近活動**: 顯示最近5次的積分記錄

### 記錄積分
- **手動輸入**: 填寫獲得積分的原因和數量
- **快速選擇**: 使用預設的常見原因快速記錄
- **即時更新**: 記錄後立即更新總積分和目標進度

### 目標管理
- **設定新目標**: 為小朋友設定新的積分目標
- **查看進度**: 詳細查看每個目標的完成進度
- **完成目標**: 當積分足夠時完成目標
- **刪除目標**: 刪除不需要的目標

### 歷史記錄
- **積分歷史**: 查看所有積分獲得記錄
- **達成的目標**: 查看已完成的目標成就

## 🎯 使用建議

### 積分設定建議
- **日常好行為**: 3-5分（如禮貌待人、按時睡覺）
- **主動幫忙**: 5-10分（如做家事、收拾玩具）
- **特別表現**: 10-20分（如主動學習、幫助他人）

### 目標設定建議
- **小目標**: 30-50分（1週內可達成）
- **中目標**: 50-100分（2-4週達成）
- **大目標**: 100-200分（1-2個月達成）

### 獎勵建議
- **小獎勵**: 小零食、貼紙、額外遊戲時間
- **中獎勵**: 小玩具、去公園、看電影
- **大獎勵**: 大玩具、去遊樂園、特別活動

## 🔧 測試功能

### 添加示範數據
1. 按 F12 打開瀏覽器開發者工具
2. 在控制台中輸入 `addDemoData()` 並按 Enter
3. 系統會自動添加一些示範數據供測試

### 清除所有數據
1. 在控制台中輸入 `clearAllData()` 並按 Enter
2. 確認後會清除所有數據

## 💡 使用技巧

### 培養習慣
- **一致性**: 每天固定時間檢查和記錄積分
- **正面鼓勵**: 重點關注正面行為，而非懲罰
- **參與感**: 讓小朋友參與目標設定過程

### 親子互動
- **共同檢視**: 每天和小朋友一起查看進度
- **慶祝成就**: 完成目標時要適當慶祝
- **調整目標**: 根據小朋友的表現調整目標難度

### 長期效果
- **逐步減少**: 隨著習慣養成，逐步減少積分依賴
- **內在動機**: 培養小朋友的內在動機和責任感
- **價值觀**: 通過積分系統傳達正確的價值觀

## 🛠️ 技術說明

### 數據存儲
- 所有數據存儲在瀏覽器的 LocalStorage 中
- 數據會自動保存，無需手動操作
- 清除瀏覽器數據會導致資料遺失

### 瀏覽器支援
- Chrome、Firefox、Safari、Edge 等現代瀏覽器
- 支援手機和平板設備
- 需要啟用 JavaScript

### 離線使用
- 應用可以完全離線使用
- 不需要網路連接
- 數據存儲在本地設備

## ❓ 常見問題

**Q: 數據會遺失嗎？**
A: 數據存儲在瀏覽器本地，除非清除瀏覽器數據，否則不會遺失。

**Q: 可以在多個設備上使用嗎？**
A: 每個設備的數據是獨立的，如需同步需要手動備份。

**Q: 小朋友可以自己操作嗎？**
A: 建議由家長操作，小朋友可以參與查看進度。

**Q: 如何備份數據？**
A: 目前需要手動記錄重要數據，未來版本會加入備份功能。

## 🎉 開始使用

現在您已經了解了所有功能，開始為您的小朋友設定第一個目標吧！記住，最重要的是培養良好的習慣和正面的價值觀。

祝您和小朋友使用愉快！ 🌟
