// 示範數據 - 用於測試應用功能

// 添加一些示範數據到應用中
function addDemoData() {
    if (!app) {
        console.error('應用尚未初始化');
        return;
    }

    // 清除現有數據
    app.data = {
        currentPoints: 0,
        goals: [],
        pointsHistory: [],
        achievements: []
    };

    // 添加一些積分記錄
    const demoPointsHistory = [
        {
            id: 1640995200000,
            reason: "主動幫忙做家事",
            points: 10,
            date: "2024-01-01",
            timestamp: "2024/1/1 上午9:00:00"
        },
        {
            id: 1640995260000,
            reason: "按時完成作業",
            points: 5,
            date: "2024-01-01",
            timestamp: "2024/1/1 下午2:00:00"
        },
        {
            id: 1640995320000,
            reason: "主動收拾玩具",
            points: 5,
            date: "2024-01-02",
            timestamp: "2024/1/2 上午10:00:00"
        },
        {
            id: 1640995380000,
            reason: "禮貌待人",
            points: 3,
            date: "2024-01-02",
            timestamp: "2024/1/2 下午3:00:00"
        },
        {
            id: 1640995440000,
            reason: "早睡早起",
            points: 5,
            date: "2024-01-03",
            timestamp: "2024/1/3 上午8:00:00"
        }
    ];

    // 添加一些目標
    const demoGoals = [
        {
            id: 1640995500000,
            title: "買新玩具車",
            targetPoints: 50,
            isCompleted: false,
            createdAt: "2024-01-01"
        },
        {
            id: 1640995560000,
            title: "去遊樂園",
            targetPoints: 100,
            isCompleted: false,
            createdAt: "2024-01-01"
        }
    ];

    // 添加一些成就
    const demoAchievements = [
        {
            goalTitle: "買故事書",
            targetPoints: 30,
            completedAt: "2023/12/25 下午4:00:00"
        },
        {
            goalTitle: "買小零食",
            targetPoints: 20,
            completedAt: "2023/12/20 上午11:00:00"
        }
    ];

    // 計算當前積分
    const totalPoints = demoPointsHistory.reduce((sum, record) => sum + record.points, 0);

    // 設置數據
    app.data.pointsHistory = demoPointsHistory;
    app.data.goals = demoGoals;
    app.data.achievements = demoAchievements;
    app.data.currentPoints = totalPoints;

    // 保存並更新顯示
    app.saveData();
    app.updateDisplay();

    console.log('示範數據已添加！');
    app.showMessage('示範數據已載入', 'success');
}

// 清除所有數據
function clearAllData() {
    if (!app) {
        console.error('應用尚未初始化');
        return;
    }

    if (confirm('確定要清除所有數據嗎？這個操作無法撤銷！')) {
        localStorage.removeItem('babyAwardsData');
        app.data = app.loadData();
        app.updateDisplay();
        console.log('所有數據已清除');
        app.showMessage('所有數據已清除', 'success');
    }
}

// 在控制台中提供幫助信息
function showHelp() {
    console.log(`
=== 小朋友積分獎勵系統 - 開發者工具 ===

可用的函數：
1. addDemoData() - 添加示範數據
2. clearAllData() - 清除所有數據
3. showHelp() - 顯示此幫助信息

使用方法：
- 打開瀏覽器開發者工具 (F12)
- 在控制台中輸入函數名稱並按 Enter

示例：
> addDemoData()
> clearAllData()
    `);
}

// 當頁面載入完成後，在控制台顯示幫助信息
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('🌟 小朋友積分獎勵系統已載入');
        console.log('輸入 showHelp() 查看可用的開發者工具');
    }, 1000);
});
