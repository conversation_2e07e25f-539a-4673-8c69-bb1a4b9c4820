# 小朋友積分獎勵系統 (Baby Awards)

一個專為3-8歲小朋友設計的積分管理應用程式，幫助家長培養孩子的儲蓄習慣和目標達成能力。

## 📱 功能特色

### 🎯 核心功能
- **積分記錄**: 記錄小朋友每次獲得積分的原因和數量
- **目標設定**: 家長可以為小朋友設定積分目標（如儲夠100分買玩具）
- **進度追蹤**: 即時顯示目標達成進度
- **成就記錄**: 記錄小朋友達成的所有目標
- **歷史查看**: 查看積分獲得歷史和成就歷史

### 🎨 設計特點
- 簡潔直觀的用戶介面
- 適合家長操作的設計
- 響應式設計，支援手機和平板
- 可愛的視覺元素，吸引小朋友注意

## 🚀 技術架構

- **前端**: HTML5 + Tailwind CSS + Vanilla JavaScript
- **數據存儲**: LocalStorage (瀏覽器本地存儲)
- **響應式設計**: 支援各種螢幕尺寸

## 📋 使用場景

### 典型使用流程
1. **設定目標**: 家長為小朋友設定一個目標，例如「儲夠100分可以買新玩具」
2. **記錄積分**: 當小朋友表現良好時，家長記錄獲得積分的原因
3. **追蹤進度**: 小朋友可以看到自己離目標還有多遠
4. **達成目標**: 當積分足夠時，家長標記目標完成
5. **查看成就**: 回顧已達成的目標，增強成就感

### 積分獲得範例
- 主動幫忙做家事 (+10分)
- 按時完成作業 (+5分)
- 主動收拾玩具 (+5分)
- 禮貌待人 (+3分)
- 早睡早起 (+5分)

## 🎯 教育價值

### 培養習慣
- **儲蓄概念**: 學習延遲滿足，為目標而努力
- **目標導向**: 培養設定目標和達成目標的能力
- **責任感**: 通過積分系統學習行為的後果

### 親子互動
- **共同參與**: 家長和小朋友一起設定目標
- **正向鼓勵**: 通過積分系統給予正面回饋
- **成就分享**: 一起慶祝目標的達成

## 📱 頁面結構

### 主要頁面
1. **首頁**: 顯示當前積分、進行中的目標
2. **記錄積分**: 添加新的積分記錄
3. **目標管理**: 設定新目標、查看目標進度
4. **歷史記錄**: 查看積分歷史和成就歷史
5. **設定頁面**: 應用程式設定

## 🔧 安裝與使用

### 快速開始
1. 下載或克隆此專案
2. 在瀏覽器中打開 `index.html`
3. 開始使用！

### 瀏覽器要求
- 支援 HTML5 和 ES6+ 的現代瀏覽器
- 建議使用 Chrome、Firefox、Safari 或 Edge

## 📊 數據管理

### 本地存儲
- 所有數據存儲在瀏覽器的 LocalStorage 中
- 數據包括：積分記錄、目標設定、成就歷史
- 清除瀏覽器數據會導致資料遺失

### 數據結構
```javascript
{
  currentPoints: 85,
  goals: [
    {
      id: 1,
      title: "買新玩具",
      targetPoints: 100,
      isCompleted: false,
      createdAt: "2024-01-01"
    }
  ],
  pointsHistory: [
    {
      id: 1,
      reason: "幫忙做家事",
      points: 10,
      date: "2024-01-01"
    }
  ],
  achievements: [
    {
      goalTitle: "買故事書",
      completedAt: "2023-12-25"
    }
  ]
}
```

## 🎨 自定義

### 積分規則建議
- 日常好行為：3-5分
- 特別幫忙：5-10分
- 重大進步：10-20分
- 目標設定：50-200分

### 目標設定建議
- 小目標：50-100分（1-2週達成）
- 中目標：100-300分（1個月達成）
- 大目標：300-500分（2-3個月達成）

## 🤝 貢獻

歡迎提交問題報告和功能建議！

## 📄 授權

MIT License

---

**讓我們一起幫助小朋友養成良好的儲蓄習慣！** 🌟
