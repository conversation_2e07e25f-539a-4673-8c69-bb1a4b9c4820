<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小朋友積分獎勵系統</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white p-4 shadow-lg">
        <div class="container mx-auto">
            <h1 class="text-2xl font-bold text-center flex items-center justify-center">
                <i class="fas fa-star mr-2 text-yellow-300"></i>
                小朋友積分獎勵系統
                <i class="fas fa-star ml-2 text-yellow-300"></i>
            </h1>
        </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="bg-white shadow-md">
        <div class="container mx-auto">
            <div class="flex overflow-x-auto">
                <button class="tab-btn flex-1 py-3 px-4 text-center border-b-2 border-blue-500 text-blue-600 font-medium" data-tab="home">
                    <i class="fas fa-home mr-1"></i>首頁
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-center border-b-2 border-transparent text-gray-600 hover:text-blue-600" data-tab="add-points">
                    <i class="fas fa-plus mr-1"></i>記錄積分
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-center border-b-2 border-transparent text-gray-600 hover:text-blue-600" data-tab="goals">
                    <i class="fas fa-target mr-1"></i>目標管理
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-center border-b-2 border-transparent text-gray-600 hover:text-blue-600" data-tab="history">
                    <i class="fas fa-history mr-1"></i>歷史記錄
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto p-4 max-w-4xl">
        
        <!-- Home Tab -->
        <div id="home" class="tab-content active">
            <!-- Current Points Card -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="text-center">
                    <h2 class="text-xl font-bold text-gray-800 mb-2">目前積分</h2>
                    <div class="text-5xl font-bold text-blue-600 mb-2">
                        <span id="current-points">0</span>
                        <span class="text-2xl">分</span>
                    </div>
                    <p class="text-gray-600">繼續努力，達成更多目標！</p>
                </div>
            </div>

            <!-- Current Goals -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-bullseye mr-2 text-red-500"></i>
                    進行中的目標
                </h3>
                <div id="current-goals-list">
                    <!-- Goals will be populated by JavaScript -->
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-clock mr-2 text-green-500"></i>
                    最近活動
                </h3>
                <div id="recent-activities">
                    <!-- Recent activities will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Add Points Tab -->
        <div id="add-points" class="tab-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-plus-circle mr-2 text-green-500"></i>
                    記錄新積分
                </h2>
                
                <form id="add-points-form" class="space-y-4">
                    <div>
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">獲得積分的原因</label>
                        <input type="text" id="reason" name="reason" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：主動幫忙做家事">
                    </div>
                    
                    <div>
                        <label for="points" class="block text-sm font-medium text-gray-700 mb-2">積分數量</label>
                        <input type="number" id="points" name="points" required min="1" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="輸入積分數量">
                    </div>
                    
                    <!-- Quick Reason Buttons -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">快速選擇原因</label>
                        <div class="grid grid-cols-2 gap-2">
                            <button type="button" class="quick-reason bg-blue-100 hover:bg-blue-200 text-blue-800 py-2 px-3 rounded-md text-sm" data-reason="主動幫忙做家事" data-points="10">
                                幫忙做家事 (+10分)
                            </button>
                            <button type="button" class="quick-reason bg-green-100 hover:bg-green-200 text-green-800 py-2 px-3 rounded-md text-sm" data-reason="按時完成作業" data-points="5">
                                完成作業 (+5分)
                            </button>
                            <button type="button" class="quick-reason bg-yellow-100 hover:bg-yellow-200 text-yellow-800 py-2 px-3 rounded-md text-sm" data-reason="主動收拾玩具" data-points="5">
                                收拾玩具 (+5分)
                            </button>
                            <button type="button" class="quick-reason bg-purple-100 hover:bg-purple-200 text-purple-800 py-2 px-3 rounded-md text-sm" data-reason="禮貌待人" data-points="3">
                                禮貌待人 (+3分)
                            </button>
                        </div>
                    </div>
                    
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-md transition duration-200">
                        <i class="fas fa-save mr-2"></i>
                        記錄積分
                    </button>
                </form>
            </div>
        </div>

        <!-- Goals Tab -->
        <div id="goals" class="tab-content">
            <!-- Add New Goal -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-flag mr-2 text-red-500"></i>
                    設定新目標
                </h2>
                
                <form id="add-goal-form" class="space-y-4">
                    <div>
                        <label for="goal-title" class="block text-sm font-medium text-gray-700 mb-2">目標名稱</label>
                        <input type="text" id="goal-title" name="goal-title" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：買新玩具">
                    </div>
                    
                    <div>
                        <label for="target-points" class="block text-sm font-medium text-gray-700 mb-2">需要積分</label>
                        <input type="number" id="target-points" name="target-points" required min="1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="輸入需要的積分數量">
                    </div>
                    
                    <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-md transition duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        設定目標
                    </button>
                </form>
            </div>

            <!-- Goals List -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-list mr-2 text-blue-500"></i>
                    目標列表
                </h3>
                <div id="goals-list">
                    <!-- Goals will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- History Tab -->
        <div id="history" class="tab-content">
            <!-- Points History -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-chart-line mr-2 text-blue-500"></i>
                    積分歷史
                </h2>
                <div id="points-history">
                    <!-- Points history will be populated by JavaScript -->
                </div>
            </div>

            <!-- Achievements -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-trophy mr-2 text-yellow-500"></i>
                    達成的目標
                </h2>
                <div id="achievements-list">
                    <!-- Achievements will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </main>

    <!-- Success/Error Messages -->
    <div id="message-container" class="fixed top-20 right-4 z-50"></div>

    <script src="app.js"></script>
    <script src="demo-data.js"></script>
</body>
</html>
